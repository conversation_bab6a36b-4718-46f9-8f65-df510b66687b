package com.ygxj.psht.data.repository

object TmpRepository {

    val tmpTestList = listOf(
        TmpEntity(
            "测试你是什么性格的人?",
            "有一天当你在餐厅用膳的时候，听到柜台的服务生很惊慌的交头接耳，说有一颗炸弹被放在餐厅中，你认为歹徒会把炸弹放在什么地方?",
            listOf(
                TmpOptionEntity("厕所", TmpResultUtil.getSingleTestResult("测试你是什么性格的人?", 1)),
                TmpOptionEntity("餐厅门口", TmpResultUtil.getSingleTestResult("测试你是什么性格的人?", 2)),
                TmpOptionEntity("客人座位", TmpResultUtil.getSingleTestResult("测试你是什么性格的人?", 3)),
                TmpOptionEntity("厨房", TmpResultUtil.getSingleTestResult("测试你是什么性格的人?", 4))
            )
        ),
        TmpEntity(
            "图片测试性格",
            "观察下面的图片,看一下隐藏了什么信息?可以试着上下晃动图片或者左右晃动脑袋或者把图片尽量的缩小试试尽可能的眼睛离图片远一些。然后从下列选项中选择你第一眼看到的形象:─定要相信你的感觉哦。",
            listOf(
                TmpOptionEntity("大猩猩", TmpResultUtil.getSingleTestResult("图片测试性格", 1)),
                TmpOptionEntity("什么也看不到", TmpResultUtil.getSingleTestResult("图片测试性格", 2)),
                TmpOptionEntity("一棵树", TmpResultUtil.getSingleTestResult("图片测试性格", 3)),
                TmpOptionEntity("狮子", TmpResultUtil.getSingleTestResult("图片测试性格", 4))
            ),
            "hb18.png"
        ),
        TmpEntity(
            "从细节看性格",
            "1、将你的双手交叉环在胸前，再看看你的双手，右手臂压住左手臂的→左脑使用者，左手臂压住右手臂的→右脑使用者\n2、将双手交屋起来，看看你的双手，左手拇指被压在右手拇指下的→左脑使用者，右手拇指被压在左手拇指下的→右脑使用者",
            listOf(
                TmpOptionEntity("左左", TmpResultUtil.getSingleTestResult("从细节看性格", 1)),
                TmpOptionEntity("左右", TmpResultUtil.getSingleTestResult("从细节看性格", 2)),
                TmpOptionEntity("右左", TmpResultUtil.getSingleTestResult("从细节看性格", 3)),
                TmpOptionEntity("右右", TmpResultUtil.getSingleTestResult("从细节看性格", 4))
            )
        ),
        TmpEntity(
            "从食物中测验你的独特风格",
            "每个人都有属于自己的独特风格，要如何了解自己或别人是属于哪种类型的人?今天的测验帮助你!以下有5种食物，请你挑选出自己最喜欢的。",
            listOf(
                TmpOptionEntity("牛肉面(越辣越过瘾)", TmpResultUtil.getSingleTestResult("从食物中测验你的独特风格", 1)),
                TmpOptionEntity("海陆大餐(好吃真好吃)", TmpResultUtil.getSingleTestResult("从食物中测验你的独特风格", 2)),
                TmpOptionEntity("比萨饼(越脆越香)", TmpResultUtil.getSingleTestResult("从食物中测验你的独特风格", 3)),
                TmpOptionEntity("炸鸡块(多汁多滋味)", TmpResultUtil.getSingleTestResult("从食物中测验你的独特风格", 4)),
                TmpOptionEntity("蛋糕(越吃越高兴)", TmpResultUtil.getSingleTestResult("从食物中测验你的独特风格", 5))
            )
        ),
        TmpEntity(
            "电梯与精神年龄",
            "你去参观一座100层的大厦，进入电梯后不久突然停电了,你希望您乘坐电梯停在第几层?",
            listOf(
                TmpOptionEntity("10", TmpResultUtil.getSingleTestResult("电梯与精神年龄", 1)),
                TmpOptionEntity("20", TmpResultUtil.getSingleTestResult("电梯与精神年龄", 2)),
                TmpOptionEntity("30", TmpResultUtil.getSingleTestResult("电梯与精神年龄", 3)),
                TmpOptionEntity("40", TmpResultUtil.getSingleTestResult("电梯与精神年龄", 4)),
                TmpOptionEntity("50", TmpResultUtil.getSingleTestResult("电梯与精神年龄", 5)),
                TmpOptionEntity("60", TmpResultUtil.getSingleTestResult("电梯与精神年龄", 6))
            )
        ),
        TmpEntity(
            "你会否给人春天般的温暖?",
            "以下五种角色，你最想演其中的谁?",
            listOf(
                TmpOptionEntity("悲伤的香菇", TmpResultUtil.getSingleTestResult("你会否给人春天般的温暖?", 1)),
                TmpOptionEntity("奔波的鸭子", TmpResultUtil.getSingleTestResult("你会否给人春天般的温暖?", 2)),
                TmpOptionEntity("年迈的松树", TmpResultUtil.getSingleTestResult("你会否给人春天般的温暖?", 3)),
                TmpOptionEntity("天边的云彩", TmpResultUtil.getSingleTestResult("你会否给人春天般的温暖?", 4)),
                TmpOptionEntity("一个胖冬瓜", TmpResultUtil.getSingleTestResult("你会否给人春天般的温暖?", 5))
            )
        ),
        TmpEntity(
            "从鞋底的磨损度看你是哪种人?",
            "鞋底的哪一侧损坏最为严重呢?拿出你所有的鞋子，将损耗最严重的一双挑出，然后把鞋底描画下来。看看你的哪一侧鞋底损耗最严重?",
            listOf(
                TmpOptionEntity("右侧鞋底耗损大", TmpResultUtil.getSingleTestResult("从鞋底的磨损度看你是哪种人?", 1)),
                TmpOptionEntity("左侧鞋底耗损大", TmpResultUtil.getSingleTestResult("从鞋底的磨损度看你是哪种人?", 2)),
                TmpOptionEntity("鞋底前端耗损大", TmpResultUtil.getSingleTestResult("从鞋底的磨损度看你是哪种人?", 3)),
                TmpOptionEntity("鞋底外侧耗损大", TmpResultUtil.getSingleTestResult("从鞋底的磨损度看你是哪种人?", 4)),
                TmpOptionEntity("鞋底面耗损均匀", TmpResultUtil.getSingleTestResult("从鞋底的磨损度看你是哪种人?", 5))
            )
        ),
        TmpEntity(
            "测试你潜在是魔鬼还是天使",
            "看看上面的图片，是天使多还是魔鬼多?",
            listOf(
                TmpOptionEntity("天使多", TmpResultUtil.getSingleTestResult("测试你潜在是魔鬼还是天使", 1)),
                TmpOptionEntity("魔鬼多", TmpResultUtil.getSingleTestResult("测试你潜在是魔鬼还是天使", 2))
            ),
            "hb19.png"
        )
    )
}
