# TmpRepository 转换说明

## 概述
本次转换将 `topic.json` 和 `ResultUtil.kt` 中的内容按照 `FunnyScaleRepository` 的代码模式进行了重新组织和转换。

## 创建的文件

### 1. TmpEntity.kt
- 定义了 `TmpEntity` 数据类，对应 `FunnyScaleEntity`
- 定义了 `TmpOptionEntity` 数据类，对应 `FunnyScaleOptionEntity`
- 使用 `String` 类型存储图片资源ID（而不是 `Int`），以便支持更灵活的图片引用

### 2. TmpResultUtil.kt
- 参照 `ResultUtil.kt` 的模式创建
- 包含 `getSingleTestResult()` 方法，根据测试名称和选择返回对应结果
- 转换了原有的结果逻辑，保持了原有的测试结果内容

### 3. TmpRepository.kt
- 参照 `FunnyScaleRepository.kt` 的模式创建
- 包含 `tmpTestList` 列表，存储所有转换后的测试数据
- 每个测试都包含名称、问题、选项列表和可选的图片资源

## 转换原则

1. **只转换单题测试**：忽略了 `topic.json` 中带有跳题逻辑（包含 `jump` 字段）的复杂测试
2. **保持数据结构一致性**：严格按照 `FunnyScaleRepository` 的数据结构模式
3. **结果集成**：将测试选项的结果直接集成到选项实体中，避免运行时查询
4. **图片资源处理**：保留了原有的图片资源引用

## 转换的测试类型

包含了以下类型的心理测试：
- 性格测试
- 职业倾向测试
- 心理状态测试
- 生活态度测试
- 人际关系测试
- 等等...

## 使用方式

```kotlin
// 获取所有测试列表
val testList = TmpRepository.tmpTestList

// 获取特定测试结果
val result = TmpResultUtil.getSingleTestResult("测试名称", 选择索引)
```

## 注意事项

1. 图片资源使用字符串类型存储，需要在使用时进行相应的资源加载处理
2. 所有测试结果都已预先计算并存储在选项中，提高了访问效率
3. 保持了与原有 `FunnyScaleRepository` 相同的代码风格和结构
