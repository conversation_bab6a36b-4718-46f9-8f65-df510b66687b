@file:Suppress("DEPRECATION")

package com.ygxj.psht.ui.main.center.consultantIntro.detail

import android.os.Bundle
import com.ygxj.psht.R
import com.ygxj.psht.base.NoVMBaseFragment
import com.ygxj.psht.databinding.FragmentConsultantIntroDetailBinding
import com.ygxj.psht.entity.ConsultantEntity
import com.ygxj.psht.ui.main.order.OrderActivity
import com.ygxj.psht.util.HtmlProcessUtil
import com.ygxj.psht.util.ResUrlProcessUtil
import com.ygxj.psht.util.ext.click
import com.ygxj.psht.util.ext.loadHtml
import com.ygxj.psht.util.ext.loadImageUrl

/**
 * 咨询师介绍-详细内容
 */
class ConsultantIntroDetailFragment : NoVMBaseFragment<FragmentConsultantIntroDetailBinding>() {

    companion object {

        fun getInstance(index: Int, size: Int, consultantEntity: ConsultantEntity): ConsultantIntroDetailFragment {
            val f = ConsultantIntroDetailFragment()
            Bundle().also {
                it.putInt("index", index)
                it.putSerializable("consultantEntity", consultantEntity)
                it.putInt("size", size)
                f.arguments = it
            }
            return f
        }
    }

    override fun getLayoutId() = R.layout.fragment_consultant_intro_detail

    override fun initData() {

        // 配置webView,使其不显示白色背景
        binding.webView.setBackgroundColor(0)

        // 页数信息
        val index = arguments?.getInt("index") ?: 0
        val size = arguments?.getInt("size") ?: 0
        binding.tvPageInfo.text = "${index + 1}/$size"

        // 咨询师信息
        val consultant = arguments?.getSerializable("consultantEntity") as? ConsultantEntity ?: return
        binding.apply {
            ivAvatar.loadImageUrl(ResUrlProcessUtil.process(consultant.Avatar))
            tvNick.text = consultant.Name
            tvGender.text = "性别: ${consultant.Sex}"
            tvLevel.text = "职称: ${consultant.JiBie}"
        }
        // 咨询师简介
        val html = """
                <html>
                    <body>
                        <div style="text-indent:2em">
                            ${HtmlProcessUtil.processForWebView(consultant.Jianjie)}
                        </div>
                    </body>
                </html>
            """.trimIndent()
        binding.webView.loadHtml(html)
        // 立即预约
        binding.btnOrder.click {
            OrderActivity.start(it.context, consultant.Id)
        }
    }

}