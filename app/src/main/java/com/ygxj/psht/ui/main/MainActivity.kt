package com.ygxj.psht.ui.main

import android.content.Context
import android.content.Intent
import com.ygxj.psht.R
import com.ygxj.psht.base.NoVMBaseActivity
import com.ygxj.psht.data.UnitNameManager
import com.ygxj.psht.databinding.ActivityMainBinding
import com.ygxj.psht.ui.main.order.OrderActivity
import com.ygxj.psht.ui.main.psyInteract.PsyInteractActivity
import com.ygxj.psht.ui.main.relax.RelaxActivity
import com.ygxj.psht.ui.main.scale.list.ScaleListActivity
import com.ygxj.psht.ui.main.welcomeGuest.WelcomeGuestActivity
import com.ygxj.psht.util.PermissionUtil
import com.ygxj.psht.util.ext.click

class MainActivity : NoVMBaseActivity<ActivityMainBinding>() {

    companion object {

        fun start(context: Context) {
            val intent = Intent(context, MainActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId() = R.layout.activity_main

    override fun initData() {

        // 单位名称
        binding.tvUnitName.text = UnitNameManager.unitName

        // 迎宾宣传
        binding.btnWelcomeGuest.click {
            PermissionUtil.requestAudioPermission(it.context) { WelcomeGuestActivity.start(this) }
        }
        // 心理测评
        binding.btnScaleTest.click { ScaleListActivity.start(this) }

        // 心理自助
        binding.btnXinLiZiZhu.click {
            // SelfHelpActivity.start(this)
            PsyInteractActivity.start(this)
        }

        // 心理减压
        binding.btnXinLiJianYa.click { RelaxActivity.start(this) }

        // 咨询辅导
        binding.btnZiXunFuDao.click { OrderActivity.start(this) }
    }
}