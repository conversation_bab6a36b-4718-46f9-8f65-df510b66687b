package com.ygxj.psht.ui.main.psyInteract

import android.content.Context
import android.content.Intent
import com.ygxj.psht.R
import com.ygxj.psht.base.NoVMBaseActivity
import com.ygxj.psht.databinding.ActivityPsyInteractBinding

/**
 * 心理互动Activity
 */
class PsyInteractActivity : NoVMBaseActivity<ActivityPsyInteractBinding>() {

    companion object {

        fun start(context: Context) {
            context.startActivity(Intent(context, PsyInteractActivity::class.java))
        }
    }

    override fun getLayoutId() = R.layout.activity_psy_interact

    override fun initData() {
        binding.activity = this
    }
}