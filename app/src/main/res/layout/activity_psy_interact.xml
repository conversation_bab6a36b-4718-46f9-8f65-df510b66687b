<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="activity"
            type="android.app.Activity" />

    </data>

    <LinearLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_default_jpg"
        android:orientation="vertical"
        tools:context=".ui.main.psyInteract.PsyInteractActivity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="10dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="心理互动"
                android:textColor="@color/white"
                android:textSize="15sp"
                tools:ignore="HardcodedText" />

            <com.ygxj.psht.view.PressAlphaImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="@{()->activity.onBackPressed()}"
                android:padding="10dp"
                android:src="@mipmap/ic_back" />

        </LinearLayout>

    </LinearLayout>
</layout>